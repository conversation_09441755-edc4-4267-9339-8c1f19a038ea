<template>
	<s-layout title="我的收藏">
		<view class="container">
			<!-- tabs页签 -->
			<view class="tabs-box">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>


			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 1250rpx;box-sizing: border-box;">

				<!-- 食堂招租 -->
				<view class="recommend-box" v-if="currentTab == 0">
					<view class="rrecommList" v-for="(item,index) in collectRentList" :key="index" @click="toDetail(item)">
						<view class="reListItem">
							<!-- 左侧图片 -->
							<view class="listItem-images">
								<image v-if="item.recommend == 1"
									style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
									src="https://jiangxiaoxian.0rui.cn/hot.png"></image>
								<image v-if="item.recommend != 1"
									style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
									src=""></image>
								<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
							</view>
							<!-- 右侧内容 -->
							<view class="listItem-contents">
								<view class="title2" style="margin-bottom: 20rpx;">{{item.platform_title}}</view>
								<view class="score-box">
									<view class="fs11 flex cate">
										<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
											<view class="status-tag " style="margin-right: 20rpx;">{{e}}
											</view>
										</view>
									</view>
								</view>
								<view class="cons-third">
									<image style="width: 24rpx;height: 24rpx;"
										src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
									<view class="title3" style="margin-left: 6rpx;">{{item.address_city_text}}
									</view>
								</view>
							</view>
							<!-- 右侧收藏 -->
							<view
								style="display: grid;justify-content: center;align-items: center;margin-left: 10rpx;width: 100rpx;"
								@click="rentColect(item,index)">
								<image v-if="item.is_collect == 1" src="https://jiangxiaoxian.0rui.cn/collected.png"
									style="width: 44rpx;height: 44rpx;margin: 0 auto;"></image>
								<image v-if="item.is_collect == 0" style="width: 44rpx;height: 44rpx;margin: 0 auto;"
									src="https://jiangxiaoxian.0rui.cn/unCollect.png"></image>

								<view v-if="item.is_collect == 1" class="fs24 Regular c3" style="margin-top: 15rpx;">
									取消收藏</view>
								<view class="fs24 Regular c9" v-if="item.is_collect == 0" style="margin-top: 15rpx;">收藏
								</view>
							</view>
						</view>
					</view>
				</view>


				<!-- 平台课程 -->
				<view class="exChangeBox" v-if="currentTab == 1">
					<view class="couponsList">
						<view class="list-item" v-for="(item,index) in collectSchoolList" :key="index" @click="toDetail(item)">
							<image style="width: 330rpx;height: 200rpx;border-radius: 18rpx 18rpx 0 0;"
								:src="item.image">
							</image>
							<view class="item-text">
								<view class="fs30 c3" style="margin: 0 auto;">{{item.title}}</view>
								<view class="exchangeBtn" v-if="item.is_collect == 1" @click="classCollect(item,index)">
									取消收藏
								</view>
								<view class="exchangeBtn1" v-if="item.is_collect == 0"
									@click="classCollect(item,index)">
									收藏
								</view>
							</view>

						</view>
					</view>

				</view>

				<!-- 无数据展示 -->
				<view class="flex flex-column flex-start align-items" style="margin-top: 400rpx;"
					v-if="( currentTab == 0 && collectRentList.length == 0) || (currentTab == 1 && collectSchoolList.length == 0)">
					<image src="https://jiangxiaoxian.0rui.cn/noneList.png" mode=""
						style="width: 520rpx;height: 259rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无数据信息</view>
				</view>
			</scroll-view>



		</view>

	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		reactive,
		ref
	} from 'vue';
	import {
		onLoad,
		onReachBottom
	} from '@dcloudio/uni-app';
	import _ from 'lodash';

	const sys_navBar = sheep.$platform.navbar;

	const listQuery = ref({
		keywords: null,
		add_type: null,
		type: null,
		my: null,
		page: 1,
		rentPage: 1,
		schoolPage: 1,
		limit: 10,
		cate_ids: '',
		province: null,
		city: null,
		district: null,
		status: null,
		recommend: null,
		collect: null,
		order: null,
		nearby: null,
		latitude: null,
		longitude: '',
		area: null,
	})

	// const 
	const tabMaps = [{
			name: '食堂招租',
			value: '0',
		},
		{
			name: '平台学院',
			value: '1',
		},
	];
	const currentTab = ref(0)
	const collectRentList = ref([])
	const rentCount = ref(0)
	const collectSchoolList = ref([])
	const schoolCount = ref(0)
	const homrS = ref(false)
	const loadStatus = ref('')
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value);
		if (currentTab.value == 0) {
			collectRentList.value = [];
			getRentList();
		} else {
			collectSchoolList.value = [];
			getSchoolList();
		}
	}

	//招租收藏列表
	async function getRentList() {
		const res = await sheep.$api.rent.rentlist({
			keywords: listQuery.value.keywords,
			page: listQuery.value.rentPage,
			limit: listQuery.value.limit,
			order: 'normal',
			status: 1,
			collect: 1,

		});
		console.log('招租收藏列表', res);

		if (res.code == 1) {
			collectRentList.value = [...collectRentList.value, ...res.data.list];
			rentCount.value = res.data.count
		} else {
			// Handle case where data is not in expected format
			collectRentList.value = [];
			sheep.$helper.toast(res.msg);
			// uni.showToast({
			// 	title: res.msg,
			// 	icon: 'none'
			// })
		}
		console.log('getList', collectRentList.value);
	}
	//平台课程收藏列表
	async function getSchoolList() {
		const res = await sheep.$api.school.schoolList({
			keywords: listQuery.value.keywords,
			page: listQuery.value.schoolPage,
			limit: listQuery.value.limit,
			order: 'normal',
			collect: 1,

		});
		console.log('平台课程收藏列表', res);

		if (res.code == 1) {
			collectSchoolList.value = [...collectSchoolList.value, ...res.data.list];
			schoolCount.value = res.data.count
		} else {
			// Handle case where data is not in expected format
			collectSchoolList.value = [];
			sheep.$helper.toast(res.msg);
			// uni.showToast({
			// 	title: res.msg,
			// 	icon: 'none'
			// })
		}
		console.log('平台课程收藏列表-222', collectSchoolList.value);
	}

	//加载更多
	function onScrolltolower() {
		if (currentTab.value == 0) {
			if (collectRentList.value.length < rentCount.value) {
				listQuery.value.rentPage += 1;
				getRentList();
			}
		} else {
			if (collectSchoolList.value.length < schoolCount.value) {
				listQuery.value.schoolPage += 1;
				getSchoolList();
			}
		}

	}
	//下拉刷新
	function onS() {
		homrS.value = true
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		// if (currentTab.value == 0) {
		// 	getRentList();
		// } else {
		// 	getSchoolList();
		// }
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		if (currentTab.value == 0) {
			listQuery.value.rentPage = 1;
			collectRentList.value = [];
			loadStatus.value = "loading";
			getRentList();
		} else {
			listQuery.value.schoolPage = 1;
			collectSchoolList.value = [];
			loadStatus.value = "loading";
			getSchoolList();
		}
	}

	//招租收藏
	function rentColect(item, index) {
		console.log('收藏', item.is_collect);
		if (item.is_collect == 1) {
			collectRentList.value[index].is_collect = 0
			const data = {
				id: item.id,
				is_collect: item.is_collect
			}
			sheep.$api.rent.rentCollect(data).then((res) => {
				if (res.code == 1) {
					sheep.$helper.toast('已取消收藏');
					// uni.showToast({
					// 	title: '已取消收藏',
					// 	icon: 'none',
					// 	duration: 2000
					// })
				}
			})
		} else {
			collectRentList.value[index].is_collect = 1
			const data1 = {
				id: item.id,
				is_collect: item.is_collect
			}
			sheep.$api.rent.rentCollect(data1).then((res) => {
				if (res.code == 1) {
					sheep.$helper.toast('已收藏');
					// uni.showToast({
					// 	title: '已收藏',
					// 	icon: 'none',
					// 	duration: 2000
					// })
				}
			})
		}
	}

	//课程收藏
	function classCollect(item, index) {
		console.log('收藏', item.is_collect);
		if (item.is_collect == 1) {
			collectSchoolList.value[index].is_collect = 0
			const data = {
				id: item.id,
				is_collect: item.is_collect
			}
			sheep.$api.school.classesCollect(data).then((res) => {
				if (res.code == 1) {
					sheep.$helper.toast('已取消收藏');
					// uni.showToast({
					// 	title: '已取消收藏',
					// 	icon: 'none',
					// 	duration: 2000,
					// })
				}
			})
		} else {
			collectSchoolList.value[index].is_collect = 1
			const data1 = {
				id: item.id,
				is_collect: item.is_collect
			}
			sheep.$api.school.classesCollect(data1).then((res) => {
				if (res.code == 1) {
					sheep.$helper.toast('已收藏');
					// uni.showToast({
					// 	title: '已收藏',
					// 	icon: 'none',
					// 	duration: 2000,
					// })
				}
			})
		}
	}
	
	function toDetail(e) {
		console.log('跳转详情', e,currentTab.value);
		if(currentTab.value == 0) {
			console.log('收藏-招租');
			uni.navigateTo({
				url: '/pages/canteenRent/rentDetail?id=' + e.id
			})
		}else {
			console.log('收藏-课程');
			uni.navigateTo({
				url:'/pages/school/classesDetail?id=' + e.id
			})
		}
		
	}

	onReachBottom(() => {});
	onLoad(() => {
		getRentList();
	});
</script>

<style lang="scss" scoped>
	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.fs24 {
		font-size: 24rpx;
	}

	.Regular {
		font-weight: 400;
	}

	.c3 {
		color: #333333;
	}

	.c9 {
		color: #999999;
	}

	.container {
		// background-color: #ffffff;

		.recommend-box {
			padding: 0rpx 30rpx;

			.rrecommList {
				margin-top: 30rpx;

				.reListItem {
					background-color: #ffffff;
					width: 640rpx;
					// height: 190rpx auto;
					padding: 30rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 18rpx;

					.listItem-images {
						display: grid;
						justify-content: flex-start;

						.iamges-mainImg {
							width: 190rpx;
							height: 190rpx;
							position: relative;
							right: 0rpx;
							bottom: 18rpx;
							z-index: 1;
							border-radius: 12rpx;
						}
					}

					.listItem-contents {
						width: 326rpx;
						height: auto;
						margin-left: 20rpx;

						.score-box {
							// width: 300rpx;
							display: flex;

							.cate {
								width: 300rpx;
								height: 190rpx auto;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								white-space: nowrap;
								/* 禁止换行 */
								overflow: hidden;
								/* 超出部分隐藏 */

								.cate-e {

									.status-tag {
										padding: 5rpx 15rpx;
										border-radius: 5rpx;
										font-size: 22rpx;
										font-weight: 400;
										line-height: 22rpx;
										background-color: rgba(247, 97, 0, 0.2);
										width: 107rpx;
										height: 36rpx;
										display: flex;
										justify-content: center;
										align-items: center;
										color: #F76100;
									}
								}
							}

							.cate-e {
								width: 300rpx;
								height: 190rpx auto;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								overflow-wrap: break-word;
								word-break: break-word;
								white-space: normal;

								.status-tag {
									// padding: 5rpx 15rpx;
									border-radius: 5rpx;
									font-size: 22rpx;
									font-weight: 400;
									line-height: 22rpx;
									background-color: rgba(247, 97, 0, 0.2);
									width: 107rpx;
									height: 36rpx;
									display: flex;
									justify-content: center;
									align-items: center;
									color: #F76100;
								}
							}

						}


						.cons-third {
							margin-top: 20rpx;
							display: flex;
							justify-content: flex-start;
							align-items: center;
						}
					}
				}

			}

		}


		.exChangeBox {
			// background-color: #ffffff;
			width: 690rpx;
			margin: 0 auto;

			.couponsList {
				width: 100%;
				// padding: 0 30rpx;
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 20rpx;
				box-sizing: border-box;
				margin-top: 30rpx;


				.list-item {
					width: 330rpx;
					border-radius: 18rpx;
					box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(153, 153, 153, 0.1);
					background-color: #ffffff;

					.item-text {
						padding: 30rpx;
						display: flex;
						flex-direction: column;
						height: 212rpx;
						/* 确保 item-text 填满父容器的高度 */
						justify-content: space-between;
						/* 将内容分散对齐，顶部和底部 */

						.exchangeBtn {
							margin-top: 30rpx;
							background-color: #ffffff;
							border: 2rpx solid #999999;
							height: 70rpx;
							width: 270rpx;
							border-radius: 223rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							color: #999999;
							font-size: 28rpx;
							line-height: 30rpx;
							font-weight: 400;
						}

						.exchangeBtn1 {
							margin-top: 30rpx;
							background-color: #fcc74e;
							// border: 2rpx solid #999999;
							height: 70rpx;
							width: 270rpx;
							border-radius: 223rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							color: #333333;
							font-size: 28rpx;
							line-height: 30rpx;
							font-weight: 400;
						}

					}
				}

			}

		}

	}
</style>