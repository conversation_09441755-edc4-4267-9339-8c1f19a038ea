import request from '@/sheep/request';

export default {
	// 招租列表
	rentlist: (params) =>
		request({
			url: 'meal.information/information_list',
			method: 'GET',
			params,
			custom: {
				showLoading: false,
			},
		}),
	// 标签列表
	tagsList: (params) =>
		request({
			url: 'meal.information/cate_list',
			method: 'GET',
			params,
			custom: {
				showLoading: false,
			},
		}),
	//招租详情
	rentInfo: (id) =>
		request({
			url: 'meal.information/detail',
			method: 'GET',
			params: {
				id: id,
			},
		}),
	//获取手机号
	getPhoneNum: (data) =>
		request({
			url: 'meal.information/exchange',
			method: 'POST',
			data,
		}),
	// 项目类型
	listType: (params) =>
		request({
			url: 'meal.information/type_list',
			method: 'GET',
			params,

		}),
	//提交发布
	addRent: (data) =>
		request({
			url: 'meal.information/add',
			method: 'POST',
			data,
		}),
	//编辑提交
	editRent: (data) =>
		request({
			url: 'meal.information/edit',
			method: 'POST',
			data,
		}),
	//删除
	delRent: (data) =>
		request({
			url: 'meal.information/del',
			method: 'POST',
			data,
		}),
	//省市
	getCity: () =>
		request({
			url: 'index/city',
			method: 'GET',
		}),
	//发布须知
	fabuAgree: () =>
		request({
			url: 'meal.information/agreement',
			method: 'GET',
		}),
	//发布身份认证查询
	isAuthentied: () =>
		request({
			url: 'meal.auth/info',
			method: 'GET',
		}),
	//收藏
	rentCollect: (data) =>
		request({
			url: 'meal.information/collect',
			method: 'POST',
			data,
		}),
};