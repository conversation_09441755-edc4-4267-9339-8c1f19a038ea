<!-- 页面  -->
<template>
	<s-layout title="积分商城">
		<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
			@refresherrefresh="onS" scroll-y="true" class="flex align-items "
			style="height: 100vh;box-sizing: border-box;">
			<view class="container">
				<!-- 顶部信息 -->
				<view class="conTop">
					<view class="textLine">
						<view class="fs30 bold c3" style="line-height: 26rpx;">当前积分</view>
						<view class="fs60 bold c3" style="margin-top: 30rpx;line-height: 60rpx;">{{score}}</view>
						<view class="scoreDetail" @click="toPage('/pages/user/wallet/score')">
							<view class="fs28 c9">积分明细</view>
							<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
								src="https://jiangxiaoxian.0rui.cn/rightMore.png"></image>
						</view>
					</view>
				</view>
				<!-- tabs页签 
			<view class="tabs-box">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>-->

				<!-- 可兑换券列表 -->
				<!-- 满减券 -->
				<view class="exChangeBox">
					<view class="scoreBox"
						style="display: flex;justify-content: center;align-items: center;width: 100%;margin: 40rpx 30rpx;">
						<view style="width: 60rpx;height: 8rpx;background-color: #fcc74e;"></view>
						<view
							style="color: #333333;font-size: 42rpx;font-weight: bold;line-height: 28rpx;margin: 0 30rpx;font-family: PingFang SC;;">
							积分兑换</view>
						<view style="width: 60rpx;height: 8rpx;background-color: #fcc74e;"></view>
					</view>
					<view class="couponsList">
						<view class="list-item" v-for="(item, index) in couponList" :key="index"
							@click="openCouponPro(item)">
							<view>
								<image v-if="item.status == 1" style="width: 330rpx;height: 200rpx;"
									src="https://jiangxiaoxian.0rui.cn/fullCoupon.png">
								</image>
								<image v-else style="width: 330rpx;height: 200rpx;"
									src="https://jiangxiaoxian.0rui.cn/scoreNumCoupon.png"></image>
								<view class="item-text">
									<view class="fs30 c3" style="margin: 0 auto;text-align: center;">{{item.name}}
									</view>
									<view class="exchangeBtn">
										{{item.score}}积分兑换
									</view>
								</view>
							</view>

						</view>


					</view>

				</view>
				<!-- 次数券 
				<view class="exChangeBox">
					<view class="couponsList">
						<view class="list-item" v-for="(item, index) in couponList" :key="index" @click="openCouponPro(item)">
							<image style="width: 330rpx;height: 200rpx;"
								src="https://jiangxiaoxian.0rui.cn/scoreNumCoupon.png"></image>
							<view class="item-text">
								<view class="fs30 c3" style="margin: 0 auto;text-align: center;">{{ item.name }}</view>
								<view class="exchangeBtn">
									{{item.score}}积分兑换
								</view>
							</view>
				
						</view>
					</view>
				</view>-->





				<!-- 无数据展示 -->
				<view class="flex flex-column flex-start align-items" v-if="couponCount == 0"
					style="margin-top: 300rpx;">
					<image src="https://jiangxiaoxian.0rui.cn/noneList.png" mode=""
						style="width: 180rpx;height: 180rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无数据信息</view>
				</view>




			</view>
		</scroll-view>
		
		<su-popup :show="showCouponPro == true" type="center" round="10" :isMaskClick="false">
			<view class="popupContacted">
				<view class="contactedBox">
					<image style="width: 198rpx;height: 122rpx;position: relative;left: 12rpx;"
						src="https://jiangxiaoxian.0rui.cn/couponSuccess.png"></image>
					<view
						style="font-size: 36rpx;line-height: 50rpx;font-weight: 900;color: #3d3d3d;text-align: center;">
						是否兑换</view>
					<view
						style="font-size: 30rpx;line-height: 44rpx;font-weight: 400;color: #3d3d3d;text-align: center;">
						{{couponName}}
					</view>
				</view>
				<view class="contactBtn">
					<view class="tactBtnBox" style="color: #999999;" @click="showCouponPro = false">取消</view>
					<view class="tactBtnBox2" style="color: #333333;" @click="handleCouponSure">确定</view>
				</view>
			</view>
		</su-popup>
		
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		onLoad,
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive
	} from 'vue';
	import _ from 'lodash';

	const listQuery = ref({
		page: 1,
		limit: 10,
		list_rows: 10,
		status: 1,
	})
	const tabMaps = [{
			name: '优惠券',
			value: '1',
		},
		{
			name: '次数券',
			value: '2',
		},
	];
	const currentTab = ref(0)
	const score = ref(0)
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value);
		listQuery.value.status = e.value
		couponList.value = []
		if (currentTab.value == 0) {

		} else {

		}
		getCouponList();
	}

	//页面跳转
	const toPage = (e) => {
		uni.navigateTo({
			url: e
		})

	}

	//满减券
	const couponList = ref([])
	const couponCount = ref(0)
	const homrS = ref(false)
	async function getCouponList() {
		// status: listQuery.value.status,
		const res = await sheep.$api.app.scoreShop.fullList({
			page: listQuery.value.page,
			limit: listQuery.value.limit,
			order: 'normal'
		});
		if (res.code === 1) {
			couponList.value = [...couponList.value, ...res.data.list];
			couponCount.value = res.data.count;
			score.value = res.data.score
		} else {
			sheep.$helper.toast(res.msg);
			// uni.showToast({
			// 	title: res.msg,
			// 	icon: 'none'
			// })
		}
	}
	//加载更多
	function onScrolltolower() {
		if (couponList.value.length < couponCount.value) {
			listQuery.value.page += 1;
			getCouponList();
		}
	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		couponList.value = [];
		getCouponList();
	}

	const showCouponPro = ref(false)
	const couponName = ref('')
	const couponSelectId = ref('')

	function openCouponPro(item) {
		showCouponPro.value = true;
		couponName.value = item.name;
		couponSelectId.value = item.id
	}
	async function handleCouponSure() {
		const res = await sheep.$api.app.scoreShop.exCoupon({
			id: couponSelectId.value
		});
		if (res.code === 1) {
			sheep.$helper.toast('兑换成功');
			// uni.showToast({
			// 	title: '兑换成功',
			// 	icon: 'success'
			// })
			showCouponPro.value = false;
			listQuery.value.page = 1;
			couponList.value = []
			getCouponList();
		} else {
			sheep.$helper.toast(res.msg);
			// uni.showToast({
			// 	title: res.msg,
			// 	icon: 'error'
			// })
			showCouponPro.value = false;
		}
	}

	// 上拉加载更多
	onReachBottom(() => {
		// loadmore();
	});
	onLoad(() => {
		getCouponList();
	});
</script>

<style lang="scss" scoped>
	.fs30 {
		font-size: 30rpx;
	}

	.fs60 {
		font-size: 60rpx;
	}

	.fs28 {
		font-size: 28rpx;
	}

	.bold {
		font-weight: bold;
	}

	.c3 {
		color: #333333;
	}

	.c9 {
		color: #999999;
	}

	.container {
		background-color: #ffffff;
		height: 100vh;

		.conTop {
			width: 100%;
			height: 300rpx;
			background-image: url('https://jiangxiaoxian.0rui.cn/scoreTopBack.png');
			background-size: 100%;

			.textLine {
				position: relative;
				top: 68rpx;
				left: 60rpx;
				display: block;

				.scoreDetail {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
				}
			}
		}

		.exChangeBox {
			background-color: #ffffff;
			width: 690rpx;
			margin-top: 30rpx;

			.couponsList {
				width: 100%;
				padding: 0 30rpx;
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				;
				grid-gap: 20px;
				// display: flex;
				// justify-content: center;
				// align-items: center;


				.list-item {
					width: 330rpx;
					border-radius: 18rpx;
					box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(153, 153, 153, 0.1);

					.item-text {
						padding: 30rpx;

						.exchangeBtn {
							background-color: #fcc74e;
							height: 70rpx;
							width: 270rpx;
							margin-top: 30rpx;
							border-radius: 223rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							font-size: 28rpx;
							line-height: 30rpx;
							font-weight: 400;
						}
					}
				}

			}

		}


	}

	.popupContacted {
		width: 660rpx;
		height: 477rpx;
		background-color: #fff;
		display: grid;
		border-radius: 18rpx;

		.contactedBox {
			width: 660rpx;
			height: 325rpx;
			padding: 30rpx 0;
			// margin-top: 40rpx;
			display: grid;
			justify-content: center;
			align-items: center;
		}

		.contactBtn {
			width: 660rpx;
			height: 92rpx;
			border-top: 1rpx solid #eeeeee;
			display: flex;
			border-radius: 0 0 18rpx 18rpx;
			// margin-top: 40rpx;

			.tactBtnBox {
				width: 330rpx;
				height: 92rpx;
				border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.tactBtnBox2 {
				width: 330rpx;
				height: 92rpx;
				// border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}
</style>