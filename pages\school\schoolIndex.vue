<template>
	<s-layout title="平台学院">
		<view class="container">
			<!-- 顶部 -->
			<view class="top">
				<!-- 顶部-地址&搜索 -->
				<view class="locTop">
					<view class="locTop-right">
						<uni-section style="border-radius: 192rpx;padding: 0;" type="line">
							<uni-search-bar v-model="listQuery.keywords" style="border-radius: 192rpx;" radius="23"
								placeholder="搜索您需要的信息" bgColor="#EEEEEE" clearButton="none" cancelButton="none"
								@confirm="searchClass" />
						</uni-section>
						<!-- <uni-search-bar></uni-search-bar> @confirm="search"-->
					</view>

				</view>


			</view>

			<!-- 课程列表 -->
			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 1250rpx;box-sizing: border-box;">
				<view class="couponsList">
					<view class="list-item" v-for="(item,index) in classesList" :key="index" @click="toDetail(item)">
						<image style="width: 100%;height: 200rpx;border-radius: 30rpx 30rpx 0 0;" :src="item.image" mode="aspectFill">
						</image>
						<image v-if="item.status == 1" style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;bottom: 190rpx;left: 20rpx;"
							src="https://jiangxiaoxian.0rui.cn/schoolText.png"></image>
							<image v-if="item.status == 2" style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;bottom: 190rpx;left: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/schoolVideo.png"></image>
						<view class="item-text">
							<view class="fs30 c3 classTitle" style="">{{item.title}}</view>
							<view class="cons-third">
								<view class="title3">{{item.release_time_text}}</view>
								<view style="display: flex;">
									<image style="width: 24rpx;height: 24rpx;"
										src="https://jiangxiaoxian.0rui.cn/eye.png">
									</image>
									<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
								</view>
							</view>
						</view>

					</view>
				</view>

			</scroll-view>

		</view>
	</s-layout>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive,
	} from 'vue';
	import {
		onLoad,
		onShow,
		onPageScroll,
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		consignee,
		mobile,
		address,
		region
	} from '@/sheep/validate/form';
	import rent from '../../sheep/api/rent';
	import sheep from '@/sheep';

	const listQuery = ref({
		page: 1,
		limit: 10,
		keywords: null,
	})
	const classCount = ref(0)
	const homrS = ref(false)
	const loadStatus = ref('')
	onLoad(() => {
		getList();
	})

	onShow(() => {})

	const classesList = ref([])
	//招租列表
	async function getList() {
		const res = await sheep.$api.school.schoolList({
			keywords: listQuery.value.keywords,
			page: listQuery.value.page,
			limit: listQuery.value.limit,
			order: 'normal',
		});
		console.log('getList', res);

		if (res.data && res.data.list) {
			classesList.value = res.data.list;
			classCount.value = res.data.count
			for (let i = 0; i < classesList.value.length; i++) {
				classesList.value[i].release_time_text = classesList.value[i].release_time_text.substring(0, 10)
			}
		} else {
			classesList.value = [];
		}

		console.log('getList', classesList.value, classCount.value);
	}
	//顶部搜索
	function searchClass() {
		console.log('搜索', listQuery.value.keywords);
		getList()
	}
	//加载更多
	function onScrolltolower() {
		if (classesList.value.length < classCount.value) {
			listQuery.value.page += 1;
			getList();
		}
	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		classesList.value = [];
		getList();
		// loadStatus.value = "loading";
	}
	
	function toDetail(e){
		console.log('跳转详情',e);
		uni.navigateTo({
			url:'/pages/school/classesDetail?id=' + e.id
		})
	}
	
	
</script>

<style lang="scss" scoped>
	.search-result {
		padding-top: 10px;
		padding-bottom: 20px;
		text-align: center;
	}

	.search-result-text {
		text-align: center;
		font-size: 14px;
		color: #666;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0px;
	}

	.uni-mt-10 {
		margin-top: 10px;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}

	.container {
		width: 100%;
		height: 100%;

		.top {
			width: 100%;
			height: 80rpx;
			// background-color: #ffffff;
			// background-image: url('https://jiangxiaoxian.0rui.cn/topBack.png');
			background-size: 100%;
			padding: 30rpx;
			display: grid;
			// margin-bottom: 30rpx;

			.locTop {
				width: 96%;
				height: 70rpx;
				display: flex;
				justify-content: space-between;
				// align-items: center;

				.locTop-right {
					width: 96%;
					height: 70rpx;

				}
			}

		}


		.couponsList {
			width: 100%;
			padding: 0 30rpx;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 20rpx;
			box-sizing: border-box;


			.list-item {
				background-color: #ffffff;
				width: 100%;
				border-radius: 30rpx;
				overflow: hidden;

				.listItem-images {
					width: 330rpx;
					height: 200rpx;
					display: grid;
					justify-content: flex-start;
				}


				.item-text {
					padding: 0rpx 20rpx 0rpx 20rpx;
					height: 120rpx;
					position: relative;
					bottom: 30rpx;
					// text-align: center;

					.classTitle {
						height: 80rpx;
						width: 300rpx;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.cons-third {
						margin-top: 20rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
					}
				}
			}

		}




	}
</style>