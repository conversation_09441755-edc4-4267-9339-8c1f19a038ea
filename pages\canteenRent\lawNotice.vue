<template>
	<s-layout title="法律声明">
		<view style="padding: 30rpx;">
			<rich-text style="text-align: justify;" :nodes="legal_notice_agreement"></rich-text>
		</view>
	</s-layout>
</template>

<script setup>
	import {
		ref,
		reactive
	} from 'vue'
	import {
		onLoad,
		onShow,
	} from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import rent from '../../sheep/api/rent';

	const rentId = ref('');
	const legal_notice_agreement = ref('');

	onLoad((options) => {
		console.log('options', options.id);
		if (options.id) {
			rentId.value = options.id;
			getDetail();
		}
	})

	function getDetail() {
		sheep.$api.rent.rentInfo(rentId.value).then((res) => {
			console.log('获取招租详情', res);
			if (res.code == 1) {
				 legal_notice_agreement.value = res.data.detail.legal_notice_agreement;
				 console.log(' legal_notice_agreement', legal_notice_agreement.value);
			} else {
				sheep.$helper.toast(res.msg);
				// uni.showToast({
				// 	title: res.msg,
				// 	icon: 'none'
				// })
			}
		}).catch((error) => {
			console.log('error: ', error);
			// 'error: ',
			// error;
		})

	}
</script>

<style lang="scss" scoped>
</style>